import React from 'react';
import useInvestmentCalculator from '../../hooks/useInvestmentCalculator';
import { FinanceTable } from '~/lib/pages/projectProfitability/components/FinanceTable';

const AnnualChargesTable: React.FC = () => {
  const { inputs, handleInputChange } = useInvestmentCalculator();

  const chargesData = [
    {
      label: 'Charges Locatives',
      value: inputs.chargesLocatives,
      isEditable: true,
      onChange: (val: string) =>
        handleInputChange('chargesLocatives', Number(val)),
    },
    {
      label: 'Assurances Non Occupant',
      value: inputs.assurancesNonOccupant,
      isEditable: true,
      onChange: (val: string) =>
        handleInputChange('assurancesNonOccupant', Number(val)),
    },
    {
      label: 'Assurances Emprunteur',
      value: inputs.assurancesEmprunteur,
      isEditable: true,
      onChange: (val: string) =>
        handleInputChange('assurancesEmprunteur', Number(val)),
    },
    {
      label: "Charges d'entretien",
      value: inputs.chargesEntretien,
      isEditable: true,
      onChange: (val: string) =>
        handleInputChange('chargesEntretien', Number(val)),
    },
    {
      label: 'Taxes Foncières',
      value: inputs.taxesFoncieres,
      isEditable: true,
      onChange: (val: string) =>
        handleInputChange('taxesFoncieres', Number(val)),
    },
    {
      label: 'Frais d’agences gestions locatives',
      value: inputs.fraisAgences,
      isEditable: true,
      onChange: (val: string) => handleInputChange('fraisAgences', Number(val)),
    },
    {
      label: 'Frais dossiers emprunt',
      value: inputs.fraisEmprunts,
      isEditable: true,
      onChange: (val: string) =>
        handleInputChange('fraisEmprunts', Number(val)),
    },
    {
      label: 'CFE',
      value: inputs.CFE,
      isEditable: true,
      onChange: (val: string) => handleInputChange('CFE', Number(val)),
    },
    {
      label: 'Frais Bancaires',
      value: inputs.fraisBancaires,
      isEditable: true,
      onChange: (val: string) =>
        handleInputChange('fraisBancaires', Number(val)),
    },
    {
      label: 'Intérêts d’emprunt',
      value: inputs.interetsEmprunt,
      isEditable: true,
      onChange: (val: string) =>
        handleInputChange('interetsEmprunt', Number(val)),
    },
    {
      label: 'Travaux',
      value: inputs.travauxDeductibles,
      isEditable: true,
      onChange: (val: string) =>
        handleInputChange('travauxDeductibles', Number(val)),
    },
  ];

  return (
    <FinanceTable
      title="Charges annuelles (non fluctuantes) – Année 1"
      data={chargesData}
      showComments={false}
    />
  );
};

export default AnnualChargesTable;
